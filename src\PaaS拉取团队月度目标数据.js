/**
 * 脚本名称：从PaaS拉取团队月度目标数据
 *
 * 功能描述：从PaaS系统获取团队月度目标数据，更新到WPS的"团队月度目标设定"表
 * 数据流向：PaaS品牌档案.产品开发目标表 → WPS团队月度目标设定表
 * 使用接口：/zijiezhen/test_sql (查询数据)
 * 时间范围：仅处理当前月份的数据
 * 执行频率：建议每小时执行一次
 *
 * 修改时间: 2025-07-18 18:30
 */

// ===========================================
// 配置区
// ===========================================

const CONFIG = {
  // PaaS API配置
  PAAS_API_URL: "https://4689cn93cl12.vicp.fun/api/zijiezhen/test_sql",
  PAAS_API_KEY: "5a2f4fda3ab24bd0b10067ac701fecfd",

  // PaaS表名配置
  PAAS_BRAND_ARCHIVE: "tb___f_e624233ca743e567",
  PAAS_BRAND_TARGET: "tb___f_e624233ca743e567_197f862cf351062f",

  // WPS表名配置
  WPS_TARGET_TABLE: "团队月度目标设定",
  WPS_LOG_TABLE: "脚本执行日志",

  // 字段映射配置
  FIELD_MAPPING: {
    // PaaS字段ID → WPS字段名
    f_17f9bbed442c3ac1: "团队名称",
    f_1981264ed988983b: "基础配额",
    f_198172b5df846222: "初始目标（团队统筹）",
    f_19812653e90a4a21: "调整配额（公司统筹）",
    f_19812654fa92ae27: "最终目标",
    f_197f8636118bbb37: "生效月份",
  },
};

// 全局变量
let logBuffer = [];
const scriptStartTime = new Date();

// ===========================================
// 辅助函数
// ===========================================

/**
 * 获取当前月份
 *
 * 概述: 获取当前月份的YYYY-MM格式字符串
 * 调用的函数: JavaScript内置Date对象方法
 * 返回值: string - 当前月份字符串
 * 修改时间: 2025-07-18 18:30
 */
function getCurrentMonth() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  return `${year}-${month}`;
}

/**
 * 格式化日期时间
 *
 * 概述: 将Date对象格式化为可读字符串
 * 调用的函数: JavaScript内置Date对象方法
 * 参数: date (Date) - 日期对象
 * 返回值: string - 格式化的日期时间字符串
 * 修改时间: 2025-07-18 18:30
 */
function formatDateTime(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 辅助函数：将Date对象格式化为 "YYYY/MM/DD HH:MM:SS" 字符串 (WPS DateTime 格式)
 *
 * 概述: WPS表格专用的日期时间格式化函数
 * 调用的函数: JavaScript内置Date对象方法
 * 参数: date (Date) - 要格式化的Date对象
 * 返回值: string - 格式化后的日期时间字符串
 * 修改时间: 2025-07-18 18:30
 */
function formatDateTimeForWpsTable(date) {
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    date = new Date(); // 如果日期无效，则回退到当前时间
  }
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // 月份从0开始
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * HTTP请求封装
 *
 * 概述: 向PaaS系统发送test_sql查询请求
 * 调用的函数: WPS AirScript内置HTTP.post方法
 * 参数: sql (string) - SQL查询语句
 * 返回值: object - 包含成功标志和数据的结果对象
 * 修改时间: 2025-07-18 12:30
 */
function queryPaaSData(sql) {
  const requestData = {
    sql: sql,
  };

  try {
    logBuffer.push(`[DEBUG] 向PaaS发送查询: ${sql.substring(0, 100)}...`);
    logBuffer.push(`[DEBUG] API URL: ${CONFIG.PAAS_API_URL}`);
    logBuffer.push(
      `[DEBUG] API Key: ${CONFIG.PAAS_API_KEY.substring(0, 8)}...`
    );
    logBuffer.push(`[DEBUG] 请求数据: ${JSON.stringify(requestData)}`);

    // 使用WPS AirScript的HTTP.post方法
    const response = HTTP.post(CONFIG.PAAS_API_URL, requestData, {
      headers: {
        "Content-Type": "application/json",
        "X-API-Key": CONFIG.PAAS_API_KEY,
      },
    });

    logBuffer.push(`[DEBUG] HTTP响应状态: ${response.status}`);
    logBuffer.push(`[DEBUG] HTTP响应类型: ${typeof response}`);

    // 检查响应状态
    if (response.status === 200) {
      // 获取响应JSON数据
      const result = response.json();

      logBuffer.push(`[DEBUG] 响应JSON解析成功`);
      logBuffer.push(`[DEBUG] 响应code: ${result.code}`);
      logBuffer.push(`[DEBUG] 响应message: ${result.message}`);

      // 检查API响应的code字段
      if (result.code === 200) {
        logBuffer.push(
          `[DEBUG] PaaS响应成功，返回 ${
            result.data ? result.data.length : 0
          } 条数据`
        );
        return {
          success: true,
          data: result.data || [],
        };
      } else {
        logBuffer.push(
          `[ERROR] PaaS API返回错误: code=${result.code}, message=${result.message}`
        );
        return {
          success: false,
          error: `PaaS API错误: ${result.message}`,
        };
      }
    } else {
      logBuffer.push(`[ERROR] HTTP请求失败: 状态码 ${response.status}`);

      // 尝试获取错误响应内容
      let errorText = "请求失败";
      try {
        if (response.status === 429) {
          errorText = "API请求过于频繁，请稍后再试";
        } else if (response.text && typeof response.text === "string") {
          errorText = response.text;
        } else if (response.json) {
          const errorJson = response.json();
          errorText = errorJson.message || JSON.stringify(errorJson);
        }
      } catch (e) {
        errorText = `状态码${response.status}错误`;
      }

      logBuffer.push(`[ERROR] 响应内容: ${errorText}`);
      return {
        success: false,
        error: `HTTP ${response.status}: ${errorText}`,
      };
    }
  } catch (error) {
    logBuffer.push(`[ERROR] HTTP请求异常: ${error.message || error}`);
    return {
      success: false,
      error: `请求异常: ${error.message || error}`,
    };
  }
}

/**
 * 标准日志记录函数：写入AirScript执行日志到WPS表格
 *
 * 概述: 将AirScript的执行日志写入指定的WPS多维数据表（标准版）
 * 详细描述:
 *   1. 检查指定的日志表是否存在
 *   2. 如果日志表不存在，则创建包含两个固定字段的新表
 *   3. 如果日志表存在，则检查是否包含必需的两个字段
 *   4. 如果有必需的字段缺失，则尝试在日志表中创建这些字段
 *   5. 将传入的日志缓冲内容和脚本执行开始时间写入日志表的新记录中
 * 调用的函数:
 *   - Application.Sheet.GetSheets
 *   - Application.Sheet.CreateSheet
 *   - Application.Field.GetFields
 *   - Application.Field.CreateFields
 *   - Application.Record.CreateRecords
 *   - formatDateTimeForWpsTable
 * 参数: config (object) - 日志写入配置对象
 * 返回值: object - 包含执行状态的对象
 * 修改时间: 2025-07-18 18:30
 */
function writeAirScriptLogsToWpsTable(config) {
  // 参数解构
  const { logBuffer, logTableName, scriptStartTime } = config;

  const result = { success: false, logRecordId: null, error: null };

  if (
    typeof Application === "undefined" ||
    typeof Application.Sheet === "undefined"
  ) {
    result.error = "Application 或 Application.Sheet 未定义。可能非WPS环境。";
    console.error("[writeAirScriptLogsToWpsTable] " + result.error);
    if (logBuffer && logBuffer.length > 0) {
      console.error(
        "[writeAirScriptLogsToWpsTable] 缓存的日志:\\n" + logBuffer.join("\\n")
      );
    }
    return result;
  }

  if (!logBuffer || logBuffer.length === 0) {
    result.success = true; // 认为操作成功，因为没有日志需要写
    return result;
  }

  const logContentForTable = logBuffer.join("\\n");

  // 固定的字段定义
  const FIXED_LOG_FIELDS = [
    { name: "执行时间", type: "MultiLineText" },
    { name: "日志内容", type: "MultiLineText" },
  ];

  let logSheetId = null;
  try {
    const sheets = Application.Sheet.GetSheets();
    let existingLogSheet = null;
    if (Array.isArray(sheets)) {
      for (let i = 0; i < sheets.length; i++) {
        if (sheets[i] && String(sheets[i].name) === String(logTableName)) {
          existingLogSheet = sheets[i];
          break;
        }
      }
    }

    if (existingLogSheet) {
      logSheetId = Number(existingLogSheet.id);

      const existingFieldsResult = Application.Field.GetFields({
        SheetId: logSheetId,
      });
      const existingFieldNames = [];
      if (Array.isArray(existingFieldsResult)) {
        for (let i = 0; i < existingFieldsResult.length; i++) {
          if (existingFieldsResult[i] && existingFieldsResult[i].name != null) {
            existingFieldNames.push(String(existingFieldsResult[i].name));
          }
        }
      }

      const fieldsToAdd = [];
      for (let i = 0; i < FIXED_LOG_FIELDS.length; i++) {
        const requiredField = FIXED_LOG_FIELDS[i];
        if (
          !existingFieldNames.some(
            (name) => String(name) === String(requiredField.name)
          )
        ) {
          fieldsToAdd.push({
            name: String(requiredField.name),
            type: String(requiredField.type),
          });
        }
      }

      if (fieldsToAdd.length > 0) {
        try {
          const createFieldsResult = Application.Field.CreateFields({
            SheetId: logSheetId,
            Fields: fieldsToAdd,
          });
          if (
            !createFieldsResult ||
            !Array.isArray(createFieldsResult) ||
            createFieldsResult.length !== fieldsToAdd.length ||
            createFieldsResult.some((f) => !f || typeof f.id === "undefined")
          ) {
            console.error(
              `[错误][writeAirScriptLogsToWpsTable] 未能向 '${logTableName}' 添加部分/全部缺失字段. API响应: ${JSON.stringify(
                createFieldsResult
              )}`
            );
          }
        } catch (fieldCreationError) {
          console.error(
            `[错误][writeAirScriptLogsToWpsTable] 在为 '${logTableName}' 执行 Application.Field.CreateFields 时发生错误: ${
              fieldCreationError.message || JSON.stringify(fieldCreationError)
            }`
          );
        }
      }
    } else {
      try {
        const newSheet = Application.Sheet.CreateSheet({
          Name: String(logTableName),
          Views: [{ name: "所有日志", type: "Grid" }],
          Fields: FIXED_LOG_FIELDS,
        });
        if (newSheet && typeof newSheet.id !== "undefined") {
          logSheetId = Number(newSheet.id);
        } else {
          result.error = `创建日志表 '${logTableName}' 失败. API响应: ${JSON.stringify(
            newSheet
          )}`;
          console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
          return result;
        }
      } catch (sheetCreationError) {
        result.error = `在为 '${logTableName}' 执行 Application.Sheet.CreateSheet 时发生错误: ${
          sheetCreationError.message || JSON.stringify(sheetCreationError)
        }`;
        console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
        return result;
      }
    }

    if (logSheetId !== null) {
      const executionTimeFormatted = formatDateTimeForWpsTable(scriptStartTime);
      const recordDataFields = {
        执行时间: executionTimeFormatted,
        日志内容: logContentForTable,
      };

      try {
        const createRecordParams = {
          SheetId: logSheetId,
          Records: [{ fields: recordDataFields }],
        };
        const createResult =
          Application.Record.CreateRecords(createRecordParams);

        if (
          createResult &&
          Array.isArray(createResult) &&
          createResult.length > 0 &&
          typeof createResult[0].id !== "undefined"
        ) {
          result.success = true;
          result.logRecordId = createResult[0].id;
        } else {
          result.error = `未能将日志写入表 '${logTableName}'. API响应: ${JSON.stringify(
            createResult
          )}`;
          console.error(
            "[错误][writeAirScriptLogsToWpsTable] " +
              result.error +
              " 数据: " +
              JSON.stringify(recordDataFields)
          );
        }
      } catch (recordCreationError) {
        result.error = `在为 '${logTableName}' 执行 Application.Record.CreateRecords 时发生错误: ${
          recordCreationError.message || JSON.stringify(recordCreationError)
        }`;
        console.error(
          "[错误][writeAirScriptLogsToWpsTable] " +
            result.error +
            " 数据: " +
            JSON.stringify(recordDataFields)
        );
      }
    } else {
      result.error = "日志表的logSheetId为空，无法写入日志。";
      console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
    }
  } catch (e) {
    result.error = `在 writeAirScriptLogsToWpsTable 中发生意外错误: ${
      e.message || JSON.stringify(e)
    }`;
    console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
    if (e.stack)
      console.error(
        "[错误][writeAirScriptLogsToWpsTable] 错误堆栈: " + e.stack
      );
  }
  return result;
}

// ===========================================
// 主要函数
// ===========================================

/**
 * 检查WPS表中当前月份数据的统计情况
 *
 * 概述: 统计已存在的当前月份数据，支持增量更新
 * 调用的函数:
 *   - Application.Sheet.GetSheets
 *   - Application.Record.GetRecords
 * 参数: currentMonth (string) - 当前月份字符串
 * 返回值: object - 包含统计信息的对象
 * 修改时间: 2025-01-14 13:20
 */
function checkDataExists(currentMonth) {
  try {
    // 查找目标表格
    const sheets = Application.Sheet.GetSheets();
    let targetSheet = null;

    for (let i = 0; i < sheets.length; i++) {
      if (String(sheets[i].name).trim() === CONFIG.WPS_TARGET_TABLE) {
        targetSheet = sheets[i];
        break;
      }
    }

    if (!targetSheet) {
      logBuffer.push(
        `[INFO] 目标表格 ${CONFIG.WPS_TARGET_TABLE} 不存在，需要创建`
      );
      return { hasData: false, count: 0, teams: [] };
    }

    // 检查当前月份的数据
    const records = Application.Record.GetRecords({
      SheetId: Number(targetSheet.id),
    });

    const existingTeams = [];

    logBuffer.push(`[DEBUG] 检查现有记录，总数: ${records.length}`);

    for (let i = 0; i < records.length; i++) {
      const record = records[i];
      const monthField = record.fields["月份"];
      const teamName = String(record.fields["团队名称"] || "").trim();

      logBuffer.push(
        `[DEBUG] 记录${i}: 团队=${teamName}, 月份=${monthField} (类型: ${typeof monthField})`
      );

      // 标准化月份格式
      let normalizedMonth = "";
      if (monthField) {
        if (typeof monthField === "string") {
          if (monthField.includes("/")) {
            const parts = monthField.split("/");
            if (parts.length >= 2) {
              normalizedMonth = `${parts[0]}-${parts[1].padStart(2, "0")}`;
            }
          } else {
            normalizedMonth = monthField.substring(0, 7);
          }
        } else if (monthField instanceof Date) {
          normalizedMonth = `${monthField.getFullYear()}-${String(
            monthField.getMonth() + 1
          ).padStart(2, "0")}`;
        }
      }

      logBuffer.push(
        `[DEBUG] 标准化月份: ${normalizedMonth} vs 目标月份: ${currentMonth}`
      );

      if (normalizedMonth === currentMonth && teamName) {
        existingTeams.push(teamName);
        logBuffer.push(`[DEBUG] ✅ 匹配到现有团队: ${teamName}`);
      }
    }

    if (existingTeams.length > 0) {
      logBuffer.push(
        `[INFO] 当前月份 ${currentMonth} 已有 ${
          existingTeams.length
        } 个团队数据: ${existingTeams.join(", ")}`
      );
    } else {
      logBuffer.push(`[INFO] 当前月份 ${currentMonth} 暂无数据`);
    }

    return {
      hasData: existingTeams.length > 0,
      count: existingTeams.length,
      teams: existingTeams,
    };
  } catch (error) {
    logBuffer.push(`[ERROR] 检查数据存在性失败: ${error.message}`);
    return { hasData: false, count: 0, teams: [] };
  }
}

/**
 * 从PaaS获取团队月度目标数据
 *
 * 概述: 查询PaaS系统中当前月份的团队月度目标数据
 * 调用的函数: queryPaaSData
 * 参数: currentMonth (string) - 当前月份
 * 返回值: array - 团队目标数据数组
 * 修改时间: 2025-07-18 18:30
 */
function fetchTeamTargetData(currentMonth) {
  // 构造SQL查询语句 - 查询当前月份的数据，如果没有则查询所有团队
  const sql = `
    SELECT TOP 50
      b.f_17f9bbed442c3ac1 AS 团队名称,
      ISNULL(bt.f_197f8636118bbb37, '${currentMonth}') AS 生效月份,
      ISNULL(bt.f_1981264ed988983b, 0) AS 基础配额,
      ISNULL(bt.f_198172b5df846222, 0) AS 初始目标,
      ISNULL(bt.f_19812653e90a4a21, 0) AS 调整配额,
      ISNULL(bt.f_19812654fa92ae27, 0) AS 最终目标,
      CASE WHEN bt.f_sn IS NULL THEN '无目标数据' ELSE '有目标数据' END AS 数据状态
    FROM ${CONFIG.PAAS_BRAND_ARCHIVE} b
    LEFT JOIN ${CONFIG.PAAS_BRAND_TARGET} bt ON b.f_sn = bt.f_sn 
      AND bt.f_197f8636118bbb37 = '${currentMonth}'
    ORDER BY b.f_17f9bbed442c3ac1
  `;

  const result = queryPaaSData(sql);

  if (!result.success) {
    throw new Error(`PaaS数据查询失败: ${result.error}`);
  }

  // 过滤出有目标数据的记录和统计信息
  const allTeams = result.data || [];
  const teamsWithData = allTeams.filter(
    (team) => team.数据状态 === "有目标数据"
  );
  const teamsWithoutData = allTeams.filter(
    (team) => team.数据状态 === "无目标数据"
  );

  logBuffer.push(
    `[INFO] 总团队数: ${allTeams.length}, 有${currentMonth}目标数据: ${teamsWithData.length}, 无目标数据: ${teamsWithoutData.length}`
  );

  if (teamsWithoutData.length > 0) {
    logBuffer.push(
      `[WARN] 以下团队未设置${currentMonth}目标: ${teamsWithoutData
        .map((t) => t.团队名称)
        .join(", ")}`
    );
  }

  logBuffer.push(
    `[INFO] 从PaaS获取到 ${teamsWithData.length} 条 ${currentMonth} 的团队目标数据`
  );

  // 只返回有目标数据的团队
  return teamsWithData;
}

/**
 * 更新WPS团队目标表
 *
 * 概述: 将PaaS数据更新到WPS表格中
 * 调用的函数:
 *   - Application.Sheet.GetSheets
 *   - Application.Sheet.CreateSheet
 *   - Application.Record.GetRecords
 *   - Application.Record.UpdateRecords
 *   - Application.Record.CreateRecords
 * 参数: paasData (array) - PaaS系统返回的数据
 * 修改时间: 2025-07-18 18:30
 */
function updateWpsTable(paasData) {
  try {
    // 查找或创建目标表格
    const sheets = Application.Sheet.GetSheets();
    let targetSheet = null;

    for (let i = 0; i < sheets.length; i++) {
      if (String(sheets[i].name).trim() === CONFIG.WPS_TARGET_TABLE) {
        targetSheet = sheets[i];
        break;
      }
    }

    // 如果表格不存在，创建新表格
    if (!targetSheet) {
      logBuffer.push(`[INFO] 创建新表格: ${CONFIG.WPS_TARGET_TABLE}`);

      const newSheet = Application.Sheet.CreateSheet({
        Name: CONFIG.WPS_TARGET_TABLE,
        Views: [{ name: "表", type: "Grid" }],
        Fields: [
          { name: "团队名称", type: "MultiLineText" },
          { name: "基础配额", type: "Number" },
          { name: "月份", type: "Date" },
          { name: "初始目标（团队统筹）", type: "Number" },
          { name: "调整配额（公司统筹）", type: "Number" },
          { name: "最终目标", type: "Number" },
        ],
      });

      targetSheet = newSheet;
      logBuffer.push(`[INFO] 成功创建表格，ID: ${targetSheet.id}`);
    }

    // 获取现有记录
    let existingRecords = [];
    try {
      const recordsResponse = Application.Record.GetRecords({
        SheetId: Number(targetSheet.id),
      });
      existingRecords = recordsResponse || [];
      logBuffer.push(`[DEBUG] 成功获取现有记录: ${existingRecords.length} 条`);
    } catch (error) {
      logBuffer.push(`[ERROR] 获取现有记录失败: ${error.message}`);
      existingRecords = [];
    }

    // 处理每条PaaS数据
    const recordsToUpdate = [];
    const recordsToCreate = [];

    logBuffer.push(`[DEBUG] 开始处理 ${paasData.length} 条PaaS数据`);

    for (let i = 0; i < paasData.length; i++) {
      const paasRecord = paasData[i];
      logBuffer.push(
        `[DEBUG] 处理第 ${i + 1} 条数据: ${JSON.stringify(paasRecord)}`
      );

      // 查找是否存在相同"月份-团队名称"组合的记录
      let existingRecord = null;
      const paasTeamName = String(paasRecord.团队名称 || "").trim();
      const paasMonth = paasRecord.生效月份 || getCurrentMonth(); // 2025-07 格式

      logBuffer.push(
        `[DEBUG] 正在查找: 团队=${paasTeamName}, 月份=${paasMonth}`
      );
      logBuffer.push(`[DEBUG] 现有记录总数: ${existingRecords.length}`);

      for (let j = 0; j < existingRecords.length; j++) {
        const existingTeamName = String(
          existingRecords[j].fields["团队名称"] || ""
        ).trim();
        const existingMonth = existingRecords[j].fields["月份"];

        logBuffer.push(
          `[DEBUG] 检查现有记录 ${j}: 团队=${existingTeamName}, 月份=${existingMonth} (类型: ${typeof existingMonth})`
        );

        // 标准化月份格式进行比较
        let normalizedExistingMonth = "";
        if (existingMonth) {
          if (typeof existingMonth === "string") {
            // 如果是字符串，提取YYYY-MM部分
            if (existingMonth.includes("/")) {
              const parts = existingMonth.split("/");
              if (parts.length >= 2) {
                normalizedExistingMonth = `${parts[0]}-${parts[1].padStart(
                  2,
                  "0"
                )}`;
              }
            } else {
              normalizedExistingMonth = existingMonth.substring(0, 7);
            }
          } else if (existingMonth instanceof Date) {
            normalizedExistingMonth = `${existingMonth.getFullYear()}-${String(
              existingMonth.getMonth() + 1
            ).padStart(2, "0")}`;
          }
        }

        logBuffer.push(
          `[DEBUG] 标准化后月份比较: ${normalizedExistingMonth} vs ${paasMonth}`
        );

        // 按"月份-团队名称"组合匹配
        if (
          existingTeamName === paasTeamName &&
          normalizedExistingMonth === paasMonth
        ) {
          existingRecord = existingRecords[j];
          logBuffer.push(
            `[DEBUG] 🎯 找到现有记录: ${paasTeamName} - ${paasMonth}`
          );
          break;
        }
      }

      if (!existingRecord) {
        logBuffer.push(
          `[DEBUG] 未找到现有记录: ${paasTeamName} - ${paasMonth}`
        );
      }

      // 构造字段数据
      const fieldsData = {
        团队名称: String(paasRecord.团队名称 || ""),
        基础配额: Number(paasRecord.基础配额 || 0),
        月份: paasRecord.生效月份
          ? `${paasRecord.生效月份}/01` // 转换为 YYYY/MM/DD 格式
          : `${getCurrentMonth()}/01`, // 默认使用当前月份
        "初始目标（团队统筹）": Number(paasRecord.初始目标 || 0),
        "调整配额（公司统筹）": Number(paasRecord.调整配额 || 0),
        最终目标: Number(paasRecord.最终目标 || 0),
      };

      logBuffer.push(`[DEBUG] 构造的字段数据: ${JSON.stringify(fieldsData)}`);

      if (existingRecord) {
        // 更新现有记录
        recordsToUpdate.push({
          recordId: existingRecord.id,
          fields: fieldsData,
        });
        logBuffer.push(`[DEBUG] 将更新记录: ${paasTeamName} - ${paasMonth}`);
      } else {
        // 创建新记录
        recordsToCreate.push({
          fields: fieldsData,
        });
        logBuffer.push(`[DEBUG] 将创建新记录: ${paasTeamName} - ${paasMonth}`);
      }
    }

    // 执行更新操作
    if (recordsToUpdate.length > 0) {
      Application.Record.UpdateRecords({
        SheetId: Number(targetSheet.id),
        Records: recordsToUpdate,
      });
      logBuffer.push(`[INFO] 更新了 ${recordsToUpdate.length} 条现有记录`);
    }

    // 执行创建操作
    if (recordsToCreate.length > 0) {
      Application.Record.CreateRecords({
        SheetId: Number(targetSheet.id),
        Records: recordsToCreate,
      });
      logBuffer.push(`[INFO] 创建了 ${recordsToCreate.length} 条新记录`);
    }

    logBuffer.push(`[INFO] WPS表格更新完成`);
  } catch (error) {
    logBuffer.push(`[ERROR] WPS表格更新失败: ${error.message}`);
    throw error;
  }
}

/**
 * 测试PaaS API连通性
 *
 * 概述: 使用简单的SQL查询测试API是否可用
 * 修改时间: 2025-01-14 12:40
 */
function testPaaSConnection() {
  logBuffer.push(`[INFO] 开始测试PaaS API连通性`);

  // 使用一个简单的测试查询
  const testSql = "SELECT 1 as test_value";

  const result = queryPaaSData(testSql);

  if (result.success) {
    logBuffer.push(`[INFO] PaaS API连通性测试成功`);

    // 添加延迟避免429错误
    logBuffer.push(`[INFO] 等待3秒避免API限流...`);
    // 简单的延迟实现
    const now = Date.now();
    while (Date.now() - now < 3000) {
      // 等待3秒
    }
    return true;
  } else {
    logBuffer.push(`[ERROR] PaaS API连通性测试失败: ${result.error}`);
    return false;
  }
}

/**
 * 主函数：执行从PaaS拉取团队月度目标数据
 *
 * 概述: 脚本的主要执行逻辑
 * 调用的函数:
 *   - getCurrentMonth
 *   - checkDataExists
 *   - fetchTeamTargetData
 *   - updateWpsTable
 *   - writeAirScriptLogsToWpsTable
 * 修改时间: 2025-01-14 12:40
 */
function main() {
  try {
    logBuffer.push(
      `[INFO] ${formatDateTime(new Date())} 开始执行PaaS团队月度目标数据拉取`
    );

    // 0. 首先测试PaaS API连通性
    logBuffer.push(`[INFO] 测试PaaS API连通性`);
    const isConnected = testPaaSConnection();

    if (!isConnected) {
      logBuffer.push(`[ERROR] PaaS API连通性测试失败，停止执行`);
      return;
    }

    // 1. 获取当前月份
    const currentMonth = getCurrentMonth();
    logBuffer.push(`[INFO] 当前处理月份: ${currentMonth}`);

    // 2. 检查WPS中当前月份数据的统计情况
    const dataStatus = checkDataExists(currentMonth);

    if (dataStatus.hasData) {
      logBuffer.push(
        `[INFO] 当前月份已有${dataStatus.count}个团队数据，将进行增量更新`
      );
    } else {
      logBuffer.push(`[INFO] 当前月份暂无数据，将全量创建`);
    }

    // 3. 从PaaS获取数据
    logBuffer.push(`[INFO] 开始从PaaS拉取 ${currentMonth} 的团队目标数据`);
    const paasData = fetchTeamTargetData(currentMonth);

    if (paasData.length === 0) {
      logBuffer.push(`[WARN] PaaS系统中没有 ${currentMonth} 的团队目标数据`);
      return;
    }

    // 4. 更新WPS表格
    logBuffer.push(`[INFO] 开始更新WPS表格`);
    updateWpsTable(paasData);

    logBuffer.push(`[INFO] 脚本执行成功完成`);
  } catch (error) {
    logBuffer.push(`[ERROR] 脚本执行失败: ${error.message}`);
    console.error(`脚本执行失败: ${error.message}`);
  } finally {
    // 记录执行日志到WPS表格
    const loggingConfig = {
      logBuffer: logBuffer,
      logTableName: CONFIG.WPS_LOG_TABLE,
      scriptStartTime: scriptStartTime,
    };

    const loggingResult = writeAirScriptLogsToWpsTable(loggingConfig);

    if (!loggingResult.success) {
      console.error(
        "严重错误: 脚本完成时未能将执行日志写入WPS表: " + loggingResult.error
      );
    }
  }
}

// 执行主函数
main();
